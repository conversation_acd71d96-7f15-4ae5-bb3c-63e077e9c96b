import axios, { AxiosInstance, AxiosResponse } from 'axios';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

class APIClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: API_BASE_URL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response: AxiosResponse) => response.data,
      (error) => {
        if (error.response?.status === 401) {
          localStorage.removeItem('token');
          window.location.href = '/login';
        }
        return Promise.reject(error.response?.data || error);
      }
    );
  }

  setAuthToken(token: string) {
    this.client.defaults.headers.common.Authorization = `Bearer ${token}`;
  }

  clearAuthToken() {
    delete this.client.defaults.headers.common.Authorization;
  }

  async get<T = any>(url: string, params?: any): Promise<T> {
    return this.client.get(url, { params });
  }

  async post<T = any>(url: string, data?: any): Promise<T> {
    return this.client.post(url, data);
  }

  async put<T = any>(url: string, data?: any): Promise<T> {
    return this.client.put(url, data);
  }

  async delete<T = any>(url: string): Promise<T> {
    return this.client.delete(url);
  }

  async patch<T = any>(url: string, data?: any): Promise<T> {
    return this.client.patch(url, data);
  }
}

export const api = new APIClient();

// SWR fetcher function
export const fetcher = (url: string) => api.get(url);

// API endpoints
export const endpoints = {
  // Auth
  login: '/auth/login',
  register: '/auth/register',
  logout: '/auth/logout',
  me: '/auth/me',
  refresh: '/auth/refresh',

  // API Keys
  apiKeys: '/api/users/me/keys',
  testApiKey: '/api/users/me/keys/test',
  apiKey: (id: string) => `/api/users/me/keys/${id}`,

  // Admin - Routing Rules
  routingRules: '/api/admin/routing-rules',
  routingRule: (id: number) => `/api/admin/routing-rules/${id}`,
  reorderRoutingRules: '/api/admin/routing-rules/reorder',

  // Admin - Users
  adminUsers: '/api/admin/users',
  adminUser: (id: string) => `/api/admin/users/${id}`,

  // Admin - System
  systemHealth: '/api/admin/system/health',
  systemMetrics: '/api/admin/system/metrics',
  systemConfig: '/api/admin/system/config',

  // Webhooks
  webhooks: '/api/users/me/webhooks',
  webhook: (id: string) => `/api/users/me/webhooks/${id}`,

  // Usage
  usage: '/api/users/me/usage',
  usageStats: '/api/users/me/usage/stats',

  // Proxy
  proxy: '/api/v1/llm/proxy',

  // Health
  health: '/healthz',
  metrics: '/metrics',
} as const;

// API service functions
export const authAPI = {
  login: (email: string, password: string) =>
    api.post(endpoints.login, { email, password }),
  register: (email: string, password: string) =>
    api.post(endpoints.register, { email, password }),
  logout: () => api.post(endpoints.logout),
  me: () => api.get(endpoints.me),
  refresh: () => api.post(endpoints.refresh),
};

export const apiKeysAPI = {
  list: () => api.get(endpoints.apiKeys),
  create: (data: {
    name: string;
    provider: string;
    api_key: string;
    endpoint_site?: string;
  }) => api.post(endpoints.apiKeys, data),
  update: (id: string, data: { name?: string; api_key?: string }) =>
    api.put(endpoints.apiKey(id), data),
  delete: (id: string) => api.delete(endpoints.apiKey(id)),
  test: (data: { keyId?: string; value?: string; format?: string; endpointSite?: string }) =>
    api.post(endpoints.testApiKey, data),
};

export const routingRulesAPI = {
  list: () => api.get(endpoints.routingRules),
  create: (data: {
    identifierType: string;
    identifierVal: string;
    priorityOrder: number;
  }) => api.post(endpoints.routingRules, data),
  update: (id: number, data: Partial<{
    identifierType: string;
    identifierVal: string;
    priorityOrder: number;
  }>) => api.put(endpoints.routingRule(id), data),
  delete: (id: number) => api.delete(endpoints.routingRule(id)),
  reorder: (ruleIds: number[]) =>
    api.put(endpoints.reorderRoutingRules, { ruleIds }),
};

export const adminAPI = {
  users: {
    list: () => api.get(endpoints.adminUsers),
    get: (id: string) => api.get(endpoints.adminUser(id)),
    update: (id: string, data: any) => api.put(endpoints.adminUser(id), data),
    delete: (id: string) => api.delete(endpoints.adminUser(id)),
  },
  system: {
    health: () => api.get(endpoints.systemHealth),
    metrics: () => api.get(endpoints.systemMetrics),
    config: () => api.get(endpoints.systemConfig),
    updateConfig: (data: any) => api.put(endpoints.systemConfig, data),
  },
};

export const webhooksAPI = {
  list: () => api.get(endpoints.webhooks),
  create: (data: { url: string; type: string }) =>
    api.post(endpoints.webhooks, data),
  delete: (id: string) => api.delete(endpoints.webhook(id)),
};

export const usageAPI = {
  list: (params?: { limit?: number; offset?: number }) =>
    api.get(endpoints.usage, params),
  stats: () => api.get(endpoints.usageStats),
};

export default api;
