{"name": "llm-proxy-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"next": "14.2.5", "react": "^18.3.1", "react-dom": "^18.3.1", "typescript": "^5.5.4", "@types/node": "^20.14.12", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "tailwindcss": "^3.4.7", "autoprefixer": "^10.4.19", "postcss": "^8.4.40", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "tailwind-merge": "^2.4.0", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-tooltip": "^1.1.2", "react-hook-form": "^7.52.1", "@hookform/resolvers": "^3.9.0", "zod": "^3.23.8", "zustand": "^4.5.4", "swr": "^2.2.5", "@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "qrcode.react": "^3.1.0", "@types/qrcode.react": "^1.0.5", "lucide-react": "^0.424.0", "sonner": "^1.5.0", "axios": "^1.7.2", "date-fns": "^3.6.0"}, "devDependencies": {"eslint": "^8.57.0", "eslint-config-next": "14.2.5", "@typescript-eslint/eslint-plugin": "^7.17.0", "@typescript-eslint/parser": "^7.17.0", "@testing-library/react": "^16.0.0", "@testing-library/jest-dom": "^6.4.8", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "@types/jest": "^29.5.12"}, "engines": {"node": ">=18.0.0"}}