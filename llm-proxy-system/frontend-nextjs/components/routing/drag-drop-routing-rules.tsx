'use client';

import { useState } from 'react';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  GripVertical,
  Edit,
  Trash2,
  MoreHorizontal,
  Eye,
  EyeOff
} from 'lucide-react';

interface RoutingRule {
  id: number;
  name: string;
  description?: string;
  enabled: boolean;
  priority: number;
  conditions: {
    user_id?: string;
    model?: string;
    provider?: string;
  };
  target: {
    provider: string;
    model?: string;
  };
  created_at: string;
  updated_at: string;
}

interface DragDropRoutingRulesProps {
  rules: RoutingRule[];
  onReorder: (reorderedRules: RoutingRule[]) => void;
  onEdit: (rule: RoutingRule) => void;
  onDelete: (ruleId: number) => void;
}

interface SortableRuleItemProps {
  rule: RoutingRule;
  onEdit: (rule: RoutingRule) => void;
  onDelete: (ruleId: number) => void;
  onToggleEnabled: (ruleId: number, enabled: boolean) => void;
}

function SortableRuleItem({ rule, onEdit, onDelete, onToggleEnabled }: SortableRuleItemProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: rule.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const getConditionText = (conditions: any) => {
    const parts = [];
    if (conditions.user_id) parts.push(`User: ${conditions.user_id}`);
    if (conditions.model) parts.push(`Model: ${conditions.model}`);
    if (conditions.provider) parts.push(`Provider: ${conditions.provider}`);
    return parts.length > 0 ? parts.join(', ') : 'All requests';
  };

  const getTargetText = (target: any) => {
    const parts = [`Provider: ${target.provider}`];
    if (target.model) parts.push(`Model: ${target.model}`);
    return parts.join(', ');
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`
        flex items-center space-x-4 p-4 bg-background border rounded-lg
        ${isDragging ? 'opacity-50 shadow-lg' : 'hover:bg-muted/50'}
        ${!rule.enabled ? 'opacity-60' : ''}
      `}
    >
      {/* Drag Handle */}
      <div
        {...attributes}
        {...listeners}
        className="cursor-grab active:cursor-grabbing text-muted-foreground hover:text-foreground"
      >
        <GripVertical className="h-5 w-5" />
      </div>

      {/* Priority Badge */}
      <div className="flex-shrink-0">
        <Badge variant="outline" className="w-8 h-8 rounded-full flex items-center justify-center">
          {rule.priority}
        </Badge>
      </div>

      {/* Rule Content */}
      <div className="flex-1 min-w-0">
        <div className="flex items-center space-x-2 mb-1">
          <h4 className="font-medium truncate">{rule.name}</h4>
          <Badge variant={rule.enabled ? 'default' : 'secondary'}>
            {rule.enabled ? 'Enabled' : 'Disabled'}
          </Badge>
        </div>
        {rule.description && (
          <p className="text-sm text-muted-foreground mb-2 truncate">
            {rule.description}
          </p>
        )}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-xs">
          <div>
            <span className="font-medium">Conditions: </span>
            <span className="text-muted-foreground">{getConditionText(rule.conditions)}</span>
          </div>
          <div>
            <span className="font-medium">Target: </span>
            <span className="text-muted-foreground">{getTargetText(rule.target)}</span>
          </div>
        </div>
      </div>

      {/* Enable/Disable Toggle */}
      <div className="flex-shrink-0">
        <Switch
          checked={rule.enabled}
          onCheckedChange={(enabled) => onToggleEnabled(rule.id, enabled)}
        />
      </div>

      {/* Actions Menu */}
      <div className="flex-shrink-0">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => onEdit(rule)}>
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem 
              onClick={() => onToggleEnabled(rule.id, !rule.enabled)}
            >
              {rule.enabled ? (
                <>
                  <EyeOff className="h-4 w-4 mr-2" />
                  Disable
                </>
              ) : (
                <>
                  <Eye className="h-4 w-4 mr-2" />
                  Enable
                </>
              )}
            </DropdownMenuItem>
            <DropdownMenuItem 
              onClick={() => onDelete(rule.id)}
              className="text-red-600 focus:text-red-600"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
}

export function DragDropRoutingRules({ 
  rules, 
  onReorder, 
  onEdit, 
  onDelete 
}: DragDropRoutingRulesProps) {
  const [localRules, setLocalRules] = useState(rules);
  
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Update local rules when props change
  useState(() => {
    setLocalRules(rules);
  });

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      const oldIndex = localRules.findIndex(rule => rule.id === active.id);
      const newIndex = localRules.findIndex(rule => rule.id === over?.id);

      const reorderedRules = arrayMove(localRules, oldIndex, newIndex);
      
      // Update priorities
      const updatedRules = reorderedRules.map((rule, index) => ({
        ...rule,
        priority: index + 1
      }));

      setLocalRules(updatedRules);
      onReorder(updatedRules);
    }
  };

  const handleToggleEnabled = async (ruleId: number, enabled: boolean) => {
    // This would typically call an API to update the rule
    const updatedRules = localRules.map(rule =>
      rule.id === ruleId ? { ...rule, enabled } : rule
    );
    setLocalRules(updatedRules);
    // You might want to call an API here to persist the change
  };

  if (localRules.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="mx-auto w-24 h-24 bg-muted rounded-full flex items-center justify-center mb-4">
          <GripVertical className="h-8 w-8 text-muted-foreground" />
        </div>
        <h3 className="text-lg font-medium mb-2">No routing rules</h3>
        <p className="text-muted-foreground mb-4">
          Create your first routing rule to start managing request routing.
        </p>
      </div>
    );
  }

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragEnd={handleDragEnd}
    >
      <SortableContext items={localRules.map(rule => rule.id)} strategy={verticalListSortingStrategy}>
        <div className="space-y-3">
          {localRules.map((rule) => (
            <SortableRuleItem
              key={rule.id}
              rule={rule}
              onEdit={onEdit}
              onDelete={onDelete}
              onToggleEnabled={handleToggleEnabled}
            />
          ))}
        </div>
      </SortableContext>
    </DndContext>
  );
}
