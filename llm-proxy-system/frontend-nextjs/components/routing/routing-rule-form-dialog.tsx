'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { routingRulesAPI } from '@/lib/api';
import { toast } from 'sonner';

// Supported providers
const PROVIDERS = [
  { value: 'openai', label: 'OpenAI' },
  { value: 'anthropic', label: 'Anthropic (Claude)' },
  { value: 'google', label: 'Google (Gemini)' },
  { value: 'azure', label: 'Azure OpenAI' },
  { value: 'aws', label: 'AWS Bedrock' },
  { value: 'cohere', label: 'Cohere' },
  { value: 'huggingface', label: 'Hugging Face' },
  { value: 'replicate', label: 'Replicate' },
  { value: 'together', label: 'Together AI' },
  { value: 'perplexity', label: 'Perplexity' },
  { value: 'mistral', label: 'Mistral AI' },
  { value: 'ollama', label: 'Ollama' },
];

const formSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name must be less than 100 characters'),
  description: z.string().max(500, 'Description must be less than 500 characters').optional(),
  enabled: z.boolean().default(true),
  
  // Conditions
  condition_user_id: z.string().optional(),
  condition_model: z.string().optional(),
  condition_provider: z.string().optional(),
  
  // Target
  target_provider: z.string().min(1, 'Target provider is required'),
  target_model: z.string().optional(),
});

type FormData = z.infer<typeof formSchema>;

interface RoutingRuleFormDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  editingRule?: any;
  onSuccess: () => void;
}

export function RoutingRuleFormDialog({ 
  open, 
  onOpenChange, 
  editingRule, 
  onSuccess 
}: RoutingRuleFormDialogProps) {
  const [isLoading, setIsLoading] = useState(false);
  const isEditing = !!editingRule;

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      description: '',
      enabled: true,
      condition_user_id: '',
      condition_model: '',
      condition_provider: '',
      target_provider: '',
      target_model: '',
    },
  });

  // Reset form when dialog opens/closes or editing rule changes
  useEffect(() => {
    if (open) {
      if (editingRule) {
        form.reset({
          name: editingRule.name || '',
          description: editingRule.description || '',
          enabled: editingRule.enabled ?? true,
          condition_user_id: editingRule.conditions?.user_id || '',
          condition_model: editingRule.conditions?.model || '',
          condition_provider: editingRule.conditions?.provider || '',
          target_provider: editingRule.target?.provider || '',
          target_model: editingRule.target?.model || '',
        });
      } else {
        form.reset({
          name: '',
          description: '',
          enabled: true,
          condition_user_id: '',
          condition_model: '',
          condition_provider: '',
          target_provider: '',
          target_model: '',
        });
      }
    }
  }, [open, editingRule, form]);

  const onSubmit = async (data: FormData) => {
    try {
      setIsLoading(true);

      // Build conditions object
      const conditions: any = {};
      if (data.condition_user_id) conditions.user_id = data.condition_user_id;
      if (data.condition_model) conditions.model = data.condition_model;
      if (data.condition_provider) conditions.provider = data.condition_provider;

      // Build target object
      const target: any = {
        provider: data.target_provider,
      };
      if (data.target_model) target.model = data.target_model;

      const ruleData = {
        name: data.name,
        description: data.description || undefined,
        enabled: data.enabled,
        conditions,
        target,
      };

      if (isEditing) {
        await routingRulesAPI.update(editingRule.id, ruleData);
        toast.success('Routing rule updated successfully');
      } else {
        await routingRulesAPI.create(ruleData);
        toast.success('Routing rule created successfully');
      }

      onSuccess();
      onOpenChange(false);
    } catch (error: any) {
      console.error('Failed to save routing rule:', error);
      const errorMessage = error.response?.data?.error || 
                          error.message || 
                          'Failed to save routing rule';
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? 'Edit Routing Rule' : 'Create New Routing Rule'}
          </DialogTitle>
          <DialogDescription>
            {isEditing 
              ? 'Update the routing rule configuration.'
              : 'Create a new routing rule to control how requests are routed to providers.'
            }
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <h4 className="text-sm font-medium">Basic Information</h4>
              
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Rule Name</FormLabel>
                      <FormControl>
                        <Input placeholder="High Priority Users" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="enabled"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                      <div className="space-y-0.5">
                        <FormLabel>Enabled</FormLabel>
                        <FormDescription className="text-xs">
                          Whether this rule is active
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description (Optional)</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Route premium users to GPT-4..."
                        className="resize-none"
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Conditions */}
            <div className="space-y-4">
              <h4 className="text-sm font-medium">Conditions (When to apply this rule)</h4>
              <p className="text-xs text-muted-foreground">
                Leave fields empty to match all requests. Multiple conditions are combined with AND logic.
              </p>
              
              <div className="grid grid-cols-1 gap-4">
                <FormField
                  control={form.control}
                  name="condition_user_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>User ID (Optional)</FormLabel>
                      <FormControl>
                        <Input placeholder="user123 or leave empty for all users" {...field} />
                      </FormControl>
                      <FormDescription>
                        Apply this rule only to specific user ID
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="condition_model"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Model (Optional)</FormLabel>
                        <FormControl>
                          <Input placeholder="gpt-4, claude-3, etc." {...field} />
                        </FormControl>
                        <FormDescription>
                          Match specific model requests
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="condition_provider"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Provider (Optional)</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Any provider" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="">Any provider</SelectItem>
                            {PROVIDERS.map((provider) => (
                              <SelectItem key={provider.value} value={provider.value}>
                                {provider.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Match requests for specific provider
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </div>

            {/* Target */}
            <div className="space-y-4">
              <h4 className="text-sm font-medium">Target (Where to route matching requests)</h4>
              
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="target_provider"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Target Provider</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select provider" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {PROVIDERS.map((provider) => (
                            <SelectItem key={provider.value} value={provider.value}>
                              {provider.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="target_model"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Target Model (Optional)</FormLabel>
                      <FormControl>
                        <Input placeholder="gpt-4, claude-3-opus, etc." {...field} />
                      </FormControl>
                      <FormDescription>
                        Override the requested model
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <DialogFooter>
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <LoadingSpinner size="sm" className="mr-2" />
                    {isEditing ? 'Updating...' : 'Creating...'}
                  </>
                ) : (
                  isEditing ? 'Update Rule' : 'Create Rule'
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
