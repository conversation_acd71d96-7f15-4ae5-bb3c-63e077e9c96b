'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { 
  Key, 
  Copy, 
  CheckCircle,
  RefreshCw,
  Shield,
  AlertTriangle,
  Eye,
  EyeOff
} from 'lucide-react';
import { toast } from 'sonner';
import { api } from '@/lib/api';

interface SystemKeyManagerProps {
  currentKey: string;
  onCopy: (text: string, fieldName: string) => void;
  copiedField: string | null;
}

export function SystemKeyManager({ 
  currentKey, 
  onCopy, 
  copiedField 
}: SystemKeyManagerProps) {
  const [isRegenerating, setIsRegenerating] = useState(false);
  const [showKey, setShowKey] = useState(false);

  const maskedKey = currentKey ? `${currentKey.slice(0, 8)}...${currentKey.slice(-4)}` : '';

  const handleRegenerateKey = async () => {
    try {
      setIsRegenerating(true);
      
      // Call API to regenerate system key
      const response = await api.post('/auth/regenerate-system-key');
      
      toast.success('System API key regenerated successfully');
      
      // Refresh the page to get the new key
      window.location.reload();
    } catch (error: any) {
      console.error('Failed to regenerate system key:', error);
      const errorMessage = error.response?.data?.error || 
                          error.message || 
                          'Failed to regenerate system key';
      toast.error(errorMessage);
    } finally {
      setIsRegenerating(false);
    }
  };

  const keyCreatedDate = new Date().toLocaleDateString(); // This should come from user data
  const keyAge = Math.floor((Date.now() - new Date().getTime()) / (1000 * 60 * 60 * 24)); // This should be calculated from actual creation date

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Key className="h-5 w-5" />
          <span>System API Key</span>
        </CardTitle>
        <CardDescription>
          Manage your system-level API key for proxy access
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Key Status */}
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Status</span>
          <Badge className="bg-green-100 text-green-800">
            Active
          </Badge>
        </div>

        {/* Current Key */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Current Key</label>
          <div className="flex space-x-2">
            <Input
              value={showKey ? currentKey : maskedKey}
              readOnly
              type={showKey ? 'text' : 'password'}
              className="font-mono text-sm"
            />
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowKey(!showKey)}
            >
              {showKey ? (
                <EyeOff className="h-4 w-4" />
              ) : (
                <Eye className="h-4 w-4" />
              )}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onCopy(currentKey, 'System API Key')}
            >
              {copiedField === 'System API Key' ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : (
                <Copy className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>

        {/* Key Information */}
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-muted-foreground">Created</span>
            <div className="font-medium">{keyCreatedDate}</div>
          </div>
          <div>
            <span className="text-muted-foreground">Age</span>
            <div className="font-medium">{keyAge} days</div>
          </div>
        </div>

        {/* Security Features */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Security Features</h4>
          <div className="space-y-1">
            <div className="flex items-center space-x-2 text-sm">
              <Shield className="h-4 w-4 text-green-500" />
              <span>Encrypted at rest</span>
            </div>
            <div className="flex items-center space-x-2 text-sm">
              <Shield className="h-4 w-4 text-green-500" />
              <span>Rate limiting enabled</span>
            </div>
            <div className="flex items-center space-x-2 text-sm">
              <Shield className="h-4 w-4 text-green-500" />
              <span>Request logging (no content)</span>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="pt-4">
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button variant="outline" className="w-full" disabled={isRegenerating}>
                {isRegenerating ? (
                  <>
                    <LoadingSpinner size="sm" className="mr-2" />
                    Regenerating...
                  </>
                ) : (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Regenerate Key
                  </>
                )}
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle className="flex items-center space-x-2">
                  <AlertTriangle className="h-5 w-5 text-orange-500" />
                  <span>Regenerate System API Key</span>
                </AlertDialogTitle>
                <AlertDialogDescription>
                  This will generate a new system API key and invalidate the current one. 
                  Any applications using the current key will need to be updated immediately.
                  <br /><br />
                  <strong>This action cannot be undone.</strong>
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction
                  onClick={handleRegenerateKey}
                  className="bg-orange-600 hover:bg-orange-700"
                >
                  Regenerate Key
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>

        {/* Warning */}
        <div className="bg-orange-50 border border-orange-200 p-3 rounded-lg">
          <div className="flex items-start space-x-2">
            <AlertTriangle className="h-4 w-4 text-orange-500 mt-0.5" />
            <div className="text-sm">
              <p className="font-medium text-orange-800">Important</p>
              <p className="text-orange-700">
                Keep your system API key secure. Never share it publicly or commit it to version control.
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
