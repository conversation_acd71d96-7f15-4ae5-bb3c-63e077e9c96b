'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Globe, 
  Copy, 
  CheckCircle,
  QrCode,
  Eye,
  EyeOff,
  ExternalLink
} from 'lucide-react';
import QRCode from 'qrcode.react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';

interface ProxyEndpointCardProps {
  endpoint: string;
  apiKey: string;
  onCopy: (text: string, fieldName: string) => void;
  copiedField: string | null;
}

export function ProxyEndpointCard({ 
  endpoint, 
  apiKey, 
  onCopy, 
  copiedField 
}: ProxyEndpointCardProps) {
  const [showApiKey, setShowApiKey] = useState(false);
  const [qrDialogOpen, setQrDialogOpen] = useState(false);

  const maskedApiKey = apiKey ? `${apiKey.slice(0, 8)}...${apiKey.slice(-4)}` : '';

  const qrData = JSON.stringify({
    endpoint,
    apiKey,
    type: 'llm-proxy-config'
  });

  const endpointStatus = {
    status: 'active',
    label: 'Active',
    color: 'bg-green-100 text-green-800'
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Globe className="h-5 w-5" />
          <span>Proxy Endpoint</span>
        </CardTitle>
        <CardDescription>
          Your OpenAI-compatible API endpoint for all LLM requests
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Endpoint Status */}
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Status</span>
          <Badge className={endpointStatus.color}>
            {endpointStatus.label}
          </Badge>
        </div>

        {/* Endpoint URL */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Endpoint URL</label>
          <div className="flex space-x-2">
            <Input
              value={endpoint}
              readOnly
              className="font-mono text-sm"
            />
            <Button
              variant="outline"
              size="sm"
              onClick={() => onCopy(endpoint, 'Endpoint URL')}
            >
              {copiedField === 'Endpoint URL' ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : (
                <Copy className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>

        {/* API Key */}
        <div className="space-y-2">
          <label className="text-sm font-medium">System API Key</label>
          <div className="flex space-x-2">
            <Input
              value={showApiKey ? apiKey : maskedApiKey}
              readOnly
              type={showApiKey ? 'text' : 'password'}
              className="font-mono text-sm"
            />
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowApiKey(!showApiKey)}
            >
              {showApiKey ? (
                <EyeOff className="h-4 w-4" />
              ) : (
                <Eye className="h-4 w-4" />
              )}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onCopy(apiKey, 'API Key')}
            >
              {copiedField === 'API Key' ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : (
                <Copy className="h-4 w-4" />
              )}
            </Button>
          </div>
          <p className="text-xs text-muted-foreground">
            Use this key in the Authorization header: Bearer {maskedApiKey}
          </p>
        </div>

        {/* Actions */}
        <div className="flex space-x-2 pt-4">
          <Dialog open={qrDialogOpen} onOpenChange={setQrDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" className="flex-1">
                <QrCode className="h-4 w-4 mr-2" />
                QR Code
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>Proxy Configuration QR Code</DialogTitle>
                <DialogDescription>
                  Scan this QR code to quickly configure your mobile app or share the endpoint
                </DialogDescription>
              </DialogHeader>
              <div className="flex flex-col items-center space-y-4">
                <div className="p-4 bg-white rounded-lg">
                  <QRCode
                    value={qrData}
                    size={200}
                    level="M"
                    includeMargin={true}
                  />
                </div>
                <div className="text-center space-y-2">
                  <p className="text-sm text-muted-foreground">
                    This QR code contains your endpoint URL and API key
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onCopy(qrData, 'QR Code Data')}
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    Copy Data
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>

          <Button variant="outline" className="flex-1" asChild>
            <a href={`${endpoint}/docs`} target="_blank" rel="noopener noreferrer">
              <ExternalLink className="h-4 w-4 mr-2" />
              API Docs
            </a>
          </Button>
        </div>

        {/* Usage Info */}
        <div className="bg-muted/50 p-3 rounded-lg">
          <h4 className="text-sm font-medium mb-2">Quick Setup</h4>
          <ol className="text-xs text-muted-foreground space-y-1">
            <li>1. Copy the endpoint URL and API key above</li>
            <li>2. Replace your OpenAI base URL with our endpoint</li>
            <li>3. Use your system API key for authentication</li>
            <li>4. Make requests to any supported model</li>
          </ol>
        </div>
      </CardContent>
    </Card>
  );
}
