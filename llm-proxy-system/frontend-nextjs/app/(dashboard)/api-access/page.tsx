'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/lib/auth';
import { ProxyEndpointCard } from '@/components/api-access/proxy-endpoint-card';
import { SystemKeyManager } from '@/components/api-access/system-key-manager';
import { 
  Globe, 
  Key, 
  Copy, 
  CheckCircle,
  ExternalLink,
  Shield,
  Zap,
  Code
} from 'lucide-react';
import { toast } from 'sonner';

export default function APIAccessPage() {
  const { user } = useAuth();
  const [copiedField, setCopiedField] = useState<string | null>(null);

  const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';
  const proxyEndpoint = `${baseUrl}/v1`;
  const systemApiKey = user?.systemApiKey || '';

  const copyToClipboard = async (text: string, fieldName: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedField(fieldName);
      toast.success(`${fieldName} copied to clipboard`);
      
      // Clear the copied state after 2 seconds
      setTimeout(() => setCopiedField(null), 2000);
    } catch (error) {
      console.error('Failed to copy:', error);
      toast.error('Failed to copy to clipboard');
    }
  };

  const features = [
    {
      icon: Globe,
      title: 'OpenAI Compatible',
      description: 'Drop-in replacement for OpenAI API endpoints',
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
    },
    {
      icon: Shield,
      title: 'Secure & Private',
      description: 'Your API keys are encrypted and never logged',
      color: 'text-green-600',
      bgColor: 'bg-green-100',
    },
    {
      icon: Zap,
      title: 'High Performance',
      description: 'Optimized routing with automatic failover',
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
    },
    {
      icon: Key,
      title: 'Multi-Provider',
      description: 'Support for 12+ LLM providers in one endpoint',
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
    },
  ];

  const codeExamples = [
    {
      language: 'curl',
      title: 'cURL',
      code: `curl -X POST "${proxyEndpoint}/chat/completions" \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer ${systemApiKey}" \\
  -d '{
    "model": "gpt-4",
    "messages": [
      {"role": "user", "content": "Hello!"}
    ]
  }'`,
    },
    {
      language: 'python',
      title: 'Python',
      code: `import openai

client = openai.OpenAI(
    api_key="${systemApiKey}",
    base_url="${proxyEndpoint}"
)

response = client.chat.completions.create(
    model="gpt-4",
    messages=[
        {"role": "user", "content": "Hello!"}
    ]
)`,
    },
    {
      language: 'javascript',
      title: 'JavaScript',
      code: `import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: '${systemApiKey}',
  baseURL: '${proxyEndpoint}'
});

const response = await openai.chat.completions.create({
  model: 'gpt-4',
  messages: [
    { role: 'user', content: 'Hello!' }
  ]
});`,
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight">API Access</h1>
        <p className="text-muted-foreground">
          Access your LLM proxy endpoint and manage system configuration.
        </p>
      </div>

      {/* Features Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {features.map((feature) => (
          <Card key={feature.title}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{feature.title}</CardTitle>
              <div className={`p-2 rounded-md ${feature.bgColor}`}>
                <feature.icon className={`h-4 w-4 ${feature.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-xs text-muted-foreground">
                {feature.description}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid gap-6 lg:grid-cols-2">
        {/* Proxy Endpoint Card */}
        <ProxyEndpointCard
          endpoint={proxyEndpoint}
          apiKey={systemApiKey}
          onCopy={copyToClipboard}
          copiedField={copiedField}
        />

        {/* System Key Manager */}
        <SystemKeyManager
          currentKey={systemApiKey}
          onCopy={copyToClipboard}
          copiedField={copiedField}
        />
      </div>

      {/* Code Examples */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Code className="h-5 w-5" />
            <span>Code Examples</span>
          </CardTitle>
          <CardDescription>
            Get started with these code examples for popular programming languages
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {codeExamples.map((example) => (
            <div key={example.language} className="space-y-2">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-medium">{example.title}</h4>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyToClipboard(example.code, `${example.title} code`)}
                >
                  {copiedField === `${example.title} code` ? (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
              </div>
              <pre className="bg-muted p-4 rounded-lg text-sm overflow-x-auto">
                <code>{example.code}</code>
              </pre>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Quick Links */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Links</CardTitle>
          <CardDescription>
            Useful resources and documentation
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-3 md:grid-cols-2">
            <Button variant="outline" className="justify-start" asChild>
              <a href="/docs/api" target="_blank" rel="noopener noreferrer">
                <ExternalLink className="h-4 w-4 mr-2" />
                API Documentation
              </a>
            </Button>
            <Button variant="outline" className="justify-start" asChild>
              <a href="/docs/models" target="_blank" rel="noopener noreferrer">
                <ExternalLink className="h-4 w-4 mr-2" />
                Supported Models
              </a>
            </Button>
            <Button variant="outline" className="justify-start" asChild>
              <a href="/docs/examples" target="_blank" rel="noopener noreferrer">
                <ExternalLink className="h-4 w-4 mr-2" />
                More Examples
              </a>
            </Button>
            <Button variant="outline" className="justify-start" asChild>
              <a href="/docs/troubleshooting" target="_blank" rel="noopener noreferrer">
                <ExternalLink className="h-4 w-4 mr-2" />
                Troubleshooting
              </a>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
