'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/lib/auth';
import { useRoutingRulesStore } from '@/lib/store';
import { routingRulesAPI } from '@/lib/api';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { DragDropRoutingRules } from '@/components/routing/drag-drop-routing-rules';
import { RoutingRuleFormDialog } from '@/components/routing/routing-rule-form-dialog';
import { 
  Plus, 
  Route,
  AlertTriangle,
  Info,
  Shield
} from 'lucide-react';
import { toast } from 'sonner';

export default function AdminRoutingPage() {
  const { isAdmin } = useAuth();
  const { rules, setRules, isLoading, setLoading } = useRoutingRulesStore();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingRule, setEditingRule] = useState<any>(null);

  useEffect(() => {
    if (isAdmin) {
      loadRoutingRules();
    }
  }, [isAdmin]);

  const loadRoutingRules = async () => {
    try {
      setLoading(true);
      const response = await routingRulesAPI.list();
      setRules(response.rules || []);
    } catch (error) {
      console.error('Failed to load routing rules:', error);
      toast.error('Failed to load routing rules');
    } finally {
      setLoading(false);
    }
  };

  const handleAddRule = () => {
    setEditingRule(null);
    setIsDialogOpen(true);
  };

  const handleEditRule = (rule: any) => {
    setEditingRule(rule);
    setIsDialogOpen(true);
  };

  const handleDeleteRule = async (ruleId: number) => {
    try {
      await routingRulesAPI.delete(ruleId);
      await loadRoutingRules();
      toast.success('Routing rule deleted successfully');
    } catch (error) {
      console.error('Failed to delete routing rule:', error);
      toast.error('Failed to delete routing rule');
    }
  };

  const handleReorderRules = async (reorderedRules: any[]) => {
    try {
      const ruleIds = reorderedRules.map(rule => rule.id);
      await routingRulesAPI.reorder(ruleIds);
      setRules(reorderedRules);
      toast.success('Routing rules reordered successfully');
    } catch (error) {
      console.error('Failed to reorder routing rules:', error);
      toast.error('Failed to reorder routing rules');
      // Reload to get the correct order
      await loadRoutingRules();
    }
  };

  if (!isAdmin) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Shield className="mx-auto h-12 w-12 text-muted-foreground" />
          <h3 className="mt-2 text-sm font-medium">Access Denied</h3>
          <p className="mt-1 text-sm text-muted-foreground">
            You need admin privileges to access routing management.
          </p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Routing Rules</h1>
          <p className="text-muted-foreground">
            Manage global routing rules and priority order for LLM requests.
          </p>
        </div>
        <Button onClick={handleAddRule}>
          <Plus className="h-4 w-4 mr-2" />
          Add Rule
        </Button>
      </div>

      {/* Info Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Info className="h-5 w-5" />
            <span>How Routing Works</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <h4 className="font-medium mb-2">Priority Order</h4>
              <p className="text-sm text-muted-foreground">
                Rules are evaluated from top to bottom. The first matching rule determines 
                which provider and model to use for the request.
              </p>
            </div>
            <div>
              <h4 className="font-medium mb-2">Rule Matching</h4>
              <p className="text-sm text-muted-foreground">
                Rules can match based on user, model, provider, or custom conditions. 
                More specific rules should be placed higher in the priority order.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Stats */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Rules</CardTitle>
            <Route className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{rules.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Rules</CardTitle>
            <Route className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {rules.filter(rule => rule.enabled).length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Disabled Rules</CardTitle>
            <AlertTriangle className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">
              {rules.filter(rule => !rule.enabled).length}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Routing Rules */}
      <Card>
        <CardHeader>
          <CardTitle>Routing Rules Management</CardTitle>
          <CardDescription>
            Drag and drop to reorder rules. Higher rules have higher priority.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <DragDropRoutingRules
            rules={rules}
            onReorder={handleReorderRules}
            onEdit={handleEditRule}
            onDelete={handleDeleteRule}
          />
        </CardContent>
      </Card>

      {/* Add/Edit Dialog */}
      <RoutingRuleFormDialog
        open={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        editingRule={editingRule}
        onSuccess={loadRoutingRules}
      />
    </div>
  );
}
